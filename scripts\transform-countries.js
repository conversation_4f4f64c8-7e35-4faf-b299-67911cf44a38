const fs = require('fs');
const path = require('path');

// Read the original countries.json file
const originalCountriesPath = path.join(__dirname, '../prisma/data/countries.json');
const originalCountries = JSON.parse(fs.readFileSync(originalCountriesPath, 'utf8'));

// Transform the data to match Prisma schema
const transformedCountries = originalCountries.map(country => {
    return {
        isoCode2: country.iso2,
        isoCode3: country.iso3,
        numericCode: country.numeric_code,
        name: country.name,
        phonecode: country.phonecode,
        flag: country.emoji,
        currency: country.currency,
        currencySymbol: country.currency_symbol,
        currencyName: country.currency_name,
        latitude: parseFloat(country.latitude),
        longitude: parseFloat(country.longitude),
        timezones: country.timezones.map(tz => ({
            zoneName: tz.zoneName.replace(/\\/g, '/'), // Fix escaped slashes
            gmtOffset: tz.gmtOffset,
            gmtOffsetName: tz.gmtOffsetName,
            abbreviation: tz.abbreviation,
            tzName: tz.tzName
        })),
        status: "active"
    };
});

// Write the transformed data back to the file
fs.writeFileSync(originalCountriesPath, JSON.stringify(transformedCountries, null, 4));

console.log(`Successfully transformed ${transformedCountries.length} countries to match Prisma schema format.`);
console.log('Countries data has been updated in prisma/data/countries.json');
